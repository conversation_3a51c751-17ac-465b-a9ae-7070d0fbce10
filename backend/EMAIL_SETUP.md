# Email Functionality Setup Guide

## Overview

The HERBIT quiz system now includes automatic email functionality that sends quiz invitations to users when sessions are created. The system includes smart filtering to exclude `improwised.com` email addresses from receiving emails.

## Features

- ✅ **Automatic Email Sending**: Emails are sent automatically when quiz sessions are created
- ✅ **Domain Filtering**: Excludes `improwised.com` addresses from receiving emails
- ✅ **HTML Email Templates**: Professional-looking email invitations with quiz links
- ✅ **Bulk Email Support**: Efficiently sends emails to multiple users
- ✅ **Error Handling**: Graceful handling of email sending failures
- ✅ **Session Integration**: Seamlessly integrated with existing session creation process

## Email Filtering Logic

The system implements the following filtering rules:

```python
# ✅ These emails WILL receive invitations:
<EMAIL>
<EMAIL>
<EMAIL>

# ❌ These emails will NOT receive invitations:
<EMAIL>
<EMAIL>
```

## Configuration

### 1. Install Dependencies

First, install the required email dependencies:

```bash
pip install aiosmtplib==3.0.2 email-validator==2.2.0 jinja2==3.1.4
```

### 2. Environment Variables

Add the following SMTP configuration to your `.env` file:

```env
# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=HERBIT Quiz System

# Frontend URL for quiz links
FRONTEND_URL=http://localhost:5173
```

### 3. SMTP Provider Setup

#### Gmail Setup
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password: Google Account → Security → App passwords
3. Use the App Password as `SMTP_PASSWORD`

#### Other SMTP Providers
- **Outlook/Hotmail**: `smtp-mail.outlook.com:587`
- **Yahoo**: `smtp.mail.yahoo.com:587`
- **Custom SMTP**: Use your organization's SMTP settings

## Email Template

The system uses a professional HTML email template that includes:

- 🌿 HERBIT branding
- 📋 Quiz details (assessment name, session code)
- 🚀 Direct quiz access button
- 📝 Clear instructions for taking the quiz
- ⚠️ Important warnings about browser behavior

## API Response

When sessions are created, the API response now includes email sending information:

```json
{
  "status_code": 200,
  "message": "Generated 3 session codes successfully. Sent 2 email invitations. 1 emails failed to send.",
  "data": {
    "sessions": [...],
    "emails_sent": 2,
    "emails_failed": 1
  }
}
```

## Testing

Run the email functionality test:

```bash
cd backend
python test_email_functionality.py
```

This will test:
- Email filtering logic
- Session data structure
- Quiz link generation

## Troubleshooting

### Common Issues

1. **Import Error: `aiosmtplib` not found**
   ```bash
   pip install aiosmtplib==3.0.2
   ```

2. **SMTP Authentication Failed**
   - Check username/password
   - For Gmail, use App Password instead of regular password
   - Verify SMTP host and port settings

3. **Emails Not Being Sent**
   - Check SMTP configuration in `.env`
   - Verify `FRONTEND_URL` is set correctly
   - Check server logs for error messages

4. **Emails Going to Spam**
   - Configure proper SPF/DKIM records for your domain
   - Use a reputable SMTP service
   - Include proper sender information

### Debug Mode

Enable debug logging to see detailed email sending information:

```python
from app.utils.logger import debug
debug("Email sending debug info", {"email": email, "result": result})
```

## Security Considerations

- ✅ Email addresses are filtered to prevent spam
- ✅ SMTP credentials are stored in environment variables
- ✅ Email templates are sanitized
- ✅ No sensitive data is included in email content
- ✅ Failed email attempts are logged but don't break session creation

## Integration Points

The email functionality integrates with:

1. **Session Creation** (`/admin/sessions`): Automatically sends emails after successful session creation
2. **Assessment Manager**: Retrieves assessment names for email content
3. **Hash ID Utils**: Generates secure quiz links
4. **Logger**: Records email sending results and errors

## Future Enhancements

Potential improvements for future versions:

- 📧 Email templates customization via admin panel
- 📊 Email delivery tracking and analytics
- 🔄 Retry mechanism for failed emails
- 📱 SMS notifications as alternative to email
- 🎨 Multiple email template themes
- 📅 Scheduled email sending
- 📈 Email engagement metrics

## Support

For issues or questions about the email functionality:

1. Check the server logs for error messages
2. Verify SMTP configuration
3. Test with the provided test script
4. Review this documentation for troubleshooting steps
