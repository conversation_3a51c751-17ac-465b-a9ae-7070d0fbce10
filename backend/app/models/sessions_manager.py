"""
This module provides sessions-related database operations using SQLAlchemy ORM.
"""

import asyncio
import random
from typing import Any, Dict, List, Optional

from database.db import get_db_context
from database.models import (
    Assessment,
    AssessmentQuestion,
    Question,
)
from database.models import Session as SessionModel
from database.models import (
    User,
    UserAnswer,
)
from fastapi import HTTPException, status
from sqlalchemy import and_, case, desc, func, or_
from sqlalchemy.exc import SQLAlchemyError

from ..api.middlewares.hashid_middleware import hash_ids_in_response
from ..models.assessment_manager import (
    calculate_total_score_for_assessment,
    get_performance_level_with_correct_total,
)
from ..utils.api_response import (
    error_response,
    raise_http_exception,
    success_response,
)
from ..utils.hashid_utils import decode_session_code
from ..utils.logger import (
    debug,
    error,
    info,
    log_database_error,
    warning,
)


def build_session_filter_conditions(status_filter: Optional[str]):
    """Build SQLAlchemy filter conditions for session filtering."""
    if status_filter == "pending":
        return or_(SessionModel.status == "pending", SessionModel.status == "created", SessionModel.status.is_(None))
    elif status_filter == "completed":
        return or_(SessionModel.status == "completed", SessionModel.status == "finished")
    else:
        # If status_filter is 'all' or None, no filter needed
        return None


def get_sessions_count(status_filter: Optional[str] = None) -> int:
    """Get total count of sessions based on filter."""
    try:
        with get_db_context() as db:
            query = db.query(SessionModel).join(Assessment).join(User)

            filter_condition = build_session_filter_conditions(status_filter)
            if filter_condition is not None:
                query = query.filter(filter_condition)

            return query.count()

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, status_filter=status_filter)
        return 0


def get_sessions_data(status_filter: Optional[str] = None, limit: int = 10, offset: int = 0) -> list:
    """Get paginated sessions data."""
    try:
        with get_db_context() as db:
            # Build the main query with joins
            query = (
                db.query(
                    SessionModel.id,
                    SessionModel.code.label("session_code"),
                    User.external_id.label("username"),
                    SessionModel.assessment_id,
                    SessionModel.score,
                    SessionModel.status,
                    SessionModel.created_at,
                    SessionModel.completed_at,
                    SessionModel.started_at,
                    Assessment.name.label("assessment_name"),
                    case(
                        (
                            Assessment.question_selection_mode == "fixed",
                            db.query(func.count(AssessmentQuestion.id))
                            .filter(AssessmentQuestion.assessment_id == Assessment.id)
                            .scalar_subquery(),
                        ),
                        else_=db.query(func.count(func.distinct(UserAnswer.question_id)))
                        .filter(UserAnswer.session_id == SessionModel.id)
                        .scalar_subquery(),
                    ).label("total_questions"),
                )
                .join(Assessment)
                .join(User)
            )

            # Apply status filter if provided
            filter_condition = build_session_filter_conditions(status_filter)
            if filter_condition is not None:
                query = query.filter(filter_condition)

            # Apply ordering, limit, and offset
            query = query.order_by(desc(SessionModel.created_at)).limit(limit).offset(offset)

            # Execute query and convert to list of dictionaries
            results = query.all()
            return [
                {
                    "id": row.id,
                    "session_code": row.session_code,
                    "username": row.username,
                    "assessment_id": row.assessment_id,
                    "score": row.score,
                    "status": row.status,
                    "created_at": row.created_at,
                    "completed_at": row.completed_at,
                    "started_at": row.started_at,
                    "assessment_name": row.assessment_name,
                    "total_questions": row.total_questions,
                }
                for row in results
            ]

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, status_filter=status_filter, limit=limit, offset=offset)
        return []


def get_single_session_data(session_id: Optional[int] = None, session_code: Optional[str] = None) -> Optional[Dict]:
    """
    Get a single session by ID or code with comprehensive details.

    Args:
        session_id: The session ID to search for
        session_code: The session code to search for

    Returns:
        A dictionary representing the session row, or None if not found.
    """
    if not session_id and not session_code:
        return None

    try:
        with get_db_context() as db:
            # Build the main query with joins
            query = (
                db.query(
                    SessionModel.id,
                    SessionModel.code.label("session_code"),
                    User.external_id.label("username"),
                    SessionModel.assessment_id,
                    SessionModel.score,
                    SessionModel.status,
                    SessionModel.created_at,
                    SessionModel.completed_at,
                    SessionModel.started_at,
                    Assessment.name.label("assessment_name"),
                    case(
                        (
                            Assessment.question_selection_mode == "fixed",
                            db.query(func.count(AssessmentQuestion.id))
                            .filter(AssessmentQuestion.assessment_id == Assessment.id)
                            .scalar_subquery(),
                        ),
                        else_=db.query(func.count(func.distinct(UserAnswer.question_id)))
                        .filter(UserAnswer.session_id == SessionModel.id)
                        .scalar_subquery(),
                    ).label("total_questions"),
                )
                .join(Assessment)
                .join(User)
            )

            # Apply filter based on provided parameter
            if session_id:
                query = query.filter(SessionModel.id == session_id)
            elif session_code:
                query = query.filter(SessionModel.code == session_code)

            # Execute query and get first result
            result = query.first()

            if result:
                return {
                    "id": result.id,
                    "session_code": result.session_code,
                    "username": result.username,
                    "assessment_id": result.assessment_id,
                    "score": result.score,
                    "status": result.status,
                    "created_at": result.created_at,
                    "completed_at": result.completed_at,
                    "started_at": result.started_at,
                    "assessment_name": result.assessment_name,
                    "total_questions": result.total_questions,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_id=session_id, session_code=session_code)
        return None


def generate_unique_session_code() -> str:
    """Generate a unique session code."""
    try:
        with get_db_context() as db:
            for _ in range(10):  # Max 10 attempts
                session_code = str(random.randint(100000, 999999)).zfill(6)
                existing_session = db.query(SessionModel).filter(SessionModel.code == session_code).first()
                if not existing_session:
                    return session_code

            raise Exception("Could not generate a unique session code after 10 attempts.")

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e)
        raise Exception("Database error while generating session code.")


def create_session_in_db(session_code: str, user_internal_id: int, assessment_id: int) -> int:
    """Create session in database and return session ID."""
    try:
        with get_db_context() as db:
            new_session = SessionModel(
                code=session_code, user_id=user_internal_id, assessment_id=assessment_id, status="pending"
            )
            db.add(new_session)
            db.flush()  # Flush to get the ID without committing
            return new_session.id

    except SQLAlchemyError as e:
        log_database_error(
            "insert",
            "sessions",
            e,
            session_code=session_code,
            user_internal_id=user_internal_id,
            assessment_id=assessment_id,
        )
        raise Exception("Failed to create session in database")


def find_or_create_user_by_details(display_name: str, email: str, external_id: str) -> int:
    """
    Finds a user by their unique email or creates a new one if not found.
    This is a robust "upsert" (update/insert) operation.

    Returns:
        int: The internal database ID of the user (either existing or newly created).
    """
    try:
        with get_db_context() as db:
            # First, try to find the user by their unique email.
            existing_user = db.query(User).filter(User.email == email).first()
            if existing_user:
                return existing_user.id

            # If the user doesn't exist, create a new one with all required fields.
            new_user = User(display_name=display_name, email=email, external_id=external_id)
            db.add(new_user)
            db.flush()  # Flush to get the ID without committing

            if not new_user.id:
                # This would indicate a serious database issue.
                raise Exception(f"Failed to create user with email {email}")

            return new_user.id

    except SQLAlchemyError as e:
        log_database_error("insert", "users", e, display_name=display_name, email=email, external_id=external_id)
        raise Exception(f"Database error while creating/finding user with email {email}")


def validate_assessment_exists(assessment_id: int):
    """
    Validate that assessment exists in database and return assessment data.

    Args:
        assessment_id: The assessment ID to validate

    Returns:
        dict: Assessment data if found

    Raises:
        HTTPException: If assessment not found or database error
    """
    try:
        with get_db_context() as db:
            assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
            if not assessment:
                raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")

            # Return assessment data as dictionary
            return {
                "id": assessment.id,
                "name": assessment.name,
                "description": assessment.description,
                "is_final": assessment.is_final,
                "question_selection_mode": assessment.question_selection_mode,
                "duration_minutes": assessment.duration_minutes,
                "total_questions": assessment.total_questions,
                "created_at": assessment.created_at,
                "updated_at": assessment.updated_at,
            }

    except SQLAlchemyError as e:
        log_database_error("select", "assessments", e, assessment_id=assessment_id)
        raise_http_exception(status_code=500, detail="Database error while validating assessment.")


def handle_completed_session(session_details: dict):
    """Handle already completed session."""
    debug("Session already completed, returning existing data")

    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.id == session_details["session_id"]).first()
            if session:
                data = {
                    "session_id": session_details["session_id"],
                    "obtained_score": session.score or 0,
                    "status": "completed",
                    "message": "Session was already completed",
                }
                hashed_data = hash_ids_in_response(data)
                return success_response(data=hashed_data, message="Session was already completed")

        return success_response(
            data={"status": "completed", "message": "Session was already completed"},
            message="Session was already completed",
        )

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_id=session_details["session_id"])
        return success_response(
            data={"status": "completed", "message": "Session was already completed"},
            message="Session was already completed",
        )


def handle_expired_session(session_details: dict):
    """Handle expired session by completing it."""
    debug("Session expired, but attempting to complete anyway")

    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.id == session_details["session_id"]).first()
            if session:
                session.status = "completed"
                session.completed_at = func.now()
                db.flush()

                debug(f"Successfully completed expired session {session_details['session_id']}")
                return success_response(
                    data={"status": "completed", "message": "Expired session completed"},
                    message="Session completed successfully",
                )

    except SQLAlchemyError as e:
        log_database_error("update", "sessions", e, session_id=session_details["session_id"])
        return None


def calculate_session_scores(session_details: dict) -> tuple[float, float, str]:
    """Calculate obtained score, total possible score, and performance level."""
    try:
        with get_db_context() as db:
            # Calculate obtained score from user_answers
            obtained_score_result = (
                db.query(func.coalesce(func.sum(UserAnswer.score), 0))
                .filter(UserAnswer.session_id == session_details["session_id"])
                .scalar()
            )

            obtained_score = obtained_score_result or 0

            # Calculate correct total possible score based on assessment mode
            assessment_id = session_details["assessment_id"]
            session_id = session_details["session_id"]
            total_possible_score = calculate_total_score_for_assessment(assessment_id, session_id)

            # Calculate performance level with correct total score
            performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_id)

            return obtained_score, total_possible_score, performance_level

    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, session_id=session_details["session_id"])
        return 0, 0, "Error"


def complete_session_in_db(session_details: dict, obtained_score: float):
    """Complete the session in database."""
    try:
        with get_db_context() as db:
            session = (
                db.query(SessionModel)
                .filter(and_(SessionModel.id == session_details["session_id"], SessionModel.status == "in_progress"))
                .first()
            )

            if not session:
                raise_http_exception(status_code=400, detail="Session could not be completed.")

            session.status = "completed"
            session.score = obtained_score
            session.completed_at = func.now()
            db.flush()

    except SQLAlchemyError as e:
        log_database_error(
            "update", "sessions", e, session_id=session_details["session_id"], obtained_score=obtained_score
        )
        raise_http_exception(status_code=500, detail="Database error while completing session.")


# --- Helper Function to avoid repeating the main query ---


def _get_session_base_query(db):
    """Returns the base SQLAlchemy query for session details to keep queries DRY."""
    return (
        db.query(
            SessionModel.id,
            SessionModel.code.label("session_code"),
            SessionModel.user_id,
            User.external_id.label("username"),
            User.display_name,
            SessionModel.assessment_id,
            SessionModel.score,
            SessionModel.status.label("session_status"),
            SessionModel.created_at,
            SessionModel.completed_at,
            SessionModel.started_at,
            Assessment.name.label("assessment_name"),
            Assessment.is_final,
            Assessment.question_selection_mode,
            Assessment.duration_minutes,
            Assessment.total_questions,
        )
        .join(Assessment)
        .join(User)
    )


# --- Functions for specific data retrieval and updates ---


def get_session_for_start_or_validation(session_code: str) -> Optional[Dict]:
    """
    Gets comprehensive session details by its 6-digit code.
    Used for validation and starting a session. Joins sessions, users, and assessments.
    """
    try:
        with get_db_context() as db:
            query = _get_session_base_query(db)
            result = query.filter(SessionModel.code == session_code).first()

            if result:
                return {
                    "id": result.id,
                    "session_code": result.session_code,
                    "user_id": result.user_id,
                    "username": result.username,
                    "display_name": result.display_name,
                    "assessment_id": result.assessment_id,
                    "score": result.score,
                    "session_status": result.session_status,  # Fixed: Use correct key name
                    "created_at": result.created_at,
                    "completed_at": result.completed_at,
                    "started_at": result.started_at,
                    "assessment_name": result.assessment_name,
                    "is_final": result.is_final,
                    "question_selection_mode": result.question_selection_mode,
                    "duration_minutes": result.duration_minutes,
                    "total_questions": result.total_questions,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def start_session_in_db(session_id: int) -> int:
    """
    Updates a session's status to 'in_progress' and sets the start time.
    Returns the number of rows affected (should be 1 on success, 0 on failure).
    """
    try:
        with get_db_context() as db:
            updated_rows = (
                db.query(SessionModel)
                .filter(and_(SessionModel.id == session_id, SessionModel.status == "pending"))
                .update({SessionModel.status: "in_progress", SessionModel.started_at: func.now()})
            )

            db.flush()
            return updated_rows

    except SQLAlchemyError as e:
        log_database_error("update", "sessions", e, session_id=session_id)
        return 0


def get_completed_session_for_results(session_identifier: str) -> Optional[Dict]:
    """
    Gets a completed session's details by its 6-digit code or numeric ID.
    """
    try:
        with get_db_context() as db:
            query = _get_session_base_query(db)

            if len(session_identifier) == 6 and session_identifier.isdigit():
                # It's a 6-digit session code
                query = query.filter(and_(SessionModel.code == session_identifier, SessionModel.status == "completed"))
            elif session_identifier.isdigit():
                # It's a numeric ID
                query = query.filter(
                    and_(SessionModel.id == int(session_identifier), SessionModel.status == "completed")
                )
            else:
                # It's a hash, decode it first (logic remains in the endpoint)
                # This function assumes the ID is already decoded if it's not a code/numeric ID.
                return None  # Or handle decoded ID if you pass it in

            result = query.first()
            if result:
                return {
                    "id": result.id,
                    "session_code": result.session_code,
                    "user_id": result.user_id,
                    "username": result.username,
                    "display_name": result.display_name,
                    "assessment_id": result.assessment_id,
                    "score": result.score,
                    "status": result.session_status,  # Changed from 'session_status' to 'status'
                    "created_at": result.created_at,
                    "completed_at": result.completed_at,
                    "started_at": result.started_at,
                    "assessment_name": result.assessment_name,
                    "is_final": result.is_final,
                    "question_selection_mode": result.question_selection_mode,
                    "duration_minutes": result.duration_minutes,
                    "total_questions": result.total_questions,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_identifier=session_identifier)
        return None


def get_session_answers_for_results(session_internal_id: int) -> List[Dict]:
    """
    Gets all questions, user answers, and correct answers for a given session.
    """
    try:
        with get_db_context() as db:
            results = (
                db.query(
                    UserAnswer.question_id,
                    UserAnswer.user_answer,
                    UserAnswer.is_correct,
                    UserAnswer.score,
                    Question.question,
                    Question.options,
                    Question.answer.label("correct_answer_key"),
                    Question.level,
                )
                .join(Question, UserAnswer.question_id == Question.que_id)
                .filter(UserAnswer.session_id == session_internal_id)
                .order_by(UserAnswer.created_at)
                .all()
            )

            return [
                {
                    "question_id": row.question_id,
                    "user_answer": row.user_answer,
                    "is_correct": row.is_correct,
                    "score": row.score,
                    "question": row.question,
                    "options": row.options,
                    "correct_answer_key": row.correct_answer_key,
                    "level": row.level,
                }
                for row in results
            ]

    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, session_internal_id=session_internal_id)
        return []


def get_session_user_details(session_code: str) -> Optional[Dict]:
    """
    Gets user and assessment details for a given session code in a single query.
    """
    try:
        with get_db_context() as db:
            result = (
                db.query(
                    User.external_id,
                    User.display_name,
                    Assessment.id.label("assessment_id"),
                    Assessment.name.label("assessment_name"),
                    Assessment.is_final,
                    SessionModel.status.label("session_status"),
                )
                .join(User, SessionModel.user_id == User.id)
                .join(Assessment, SessionModel.assessment_id == Assessment.id)
                .filter(SessionModel.code == session_code)
                .first()
            )

            if result:
                return {
                    "external_id": result.external_id,
                    "display_name": result.display_name,
                    "assessment_id": result.assessment_id,
                    "assessment_name": result.assessment_name,
                    "is_final": result.is_final,
                    "session_status": result.session_status,  # Fixed: use consistent key name
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def get_user_id_by_email(email: str) -> Optional[int]:
    """
    Fetches a user's internal integer ID from their email address.

    Args:
        email: The user's email address.

    Returns:
        The integer user ID, or None if not found.
    """
    try:
        with get_db_context() as db:
            user = db.query(User).filter(User.email == email).first()
            return user.id if user else None

    except SQLAlchemyError as e:
        log_database_error("select", "users", e, email=email)
        return None


def get_sessions_by_user_id(user_id: int) -> List[Dict]:
    """
    Fetches all sessions associated with a specific user ID, ordered by creation date.
    This reuses the main session query for consistency.

    Args:
        user_id: The internal integer ID of the user.

    Returns:
        A list of dictionaries, where each dictionary represents a session.
    """
    try:
        with get_db_context() as db:
            query = _get_session_base_query(db)
            results = query.filter(SessionModel.user_id == user_id).order_by(desc(SessionModel.created_at)).all()

            return [
                {
                    "id": row.id,
                    "session_code": row.session_code,
                    "user_id": row.user_id,
                    "username": row.username,
                    "display_name": row.display_name,
                    "assessment_id": row.assessment_id,
                    "score": row.score,
                    "status": row.session_status,  # Changed from 'session_status' to 'status'
                    "created_at": row.created_at,
                    "completed_at": row.completed_at,
                    "started_at": row.started_at,
                    "assessment_name": row.assessment_name,
                    "is_final": row.is_final,
                    "question_selection_mode": row.question_selection_mode,
                    "duration_minutes": row.duration_minutes,
                    "total_questions": row.total_questions,
                }
                for row in results
            ]

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, user_id=user_id)
        return []


def db_get_user_by_email(email: str) -> Optional[Dict]:
    """
    Fetches a full user record from the database by email.

    Returns:
        A dictionary representing the user, or None if not found.
    """
    try:
        with get_db_context() as db:
            user = db.query(User).filter(User.email == email).first()
            if user:
                return {
                    "id": user.id,
                    "external_id": user.external_id,
                    "email": user.email,
                    "display_name": user.display_name,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "users", e, email=email)
        return None


def db_get_user_by_external_id(external_id: str) -> Optional[Dict]:
    """
    Fetches a full user record from the database by their external_id.

    Returns:
        A dictionary representing the user, or None if not found.
    """
    try:
        with get_db_context() as db:
            user = db.query(User).filter(User.external_id == external_id).first()
            if user:
                return {
                    "id": user.id,
                    "external_id": user.external_id,
                    "email": user.email,
                    "display_name": user.display_name,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "users", e, external_id=external_id)
        return None


def db_update_user_email(user_id: int, email: str) -> int:
    """
    Executes the query to update a user's email if it is currently null or empty.

    Returns:
        The number of rows affected (0 or 1).
    """
    try:
        with get_db_context() as db:
            updated_rows = (
                db.query(User)
                .filter(and_(User.id == user_id, or_(User.email.is_(None), User.email == "")))
                .update({User.email: email})
            )

            db.flush()
            return updated_rows

    except SQLAlchemyError as e:
        log_database_error("update", "users", e, user_id=user_id, email=email)
        return 0


def db_create_user(external_id: str, email: str, display_name: str) -> int:
    """
    Executes the query to insert a new user and returns their new internal ID.

    Returns:
        The integer ID of the newly created user.
    """
    try:
        with get_db_context() as db:
            new_user = User(external_id=external_id, email=email, display_name=display_name)
            db.add(new_user)
            db.flush()
            return new_user.id

    except SQLAlchemyError as e:
        log_database_error("insert", "users", e, external_id=external_id, email=email, display_name=display_name)
        raise Exception(f"Failed to create user with external_id {external_id}")


def _find_session_by_code(session_code: str) -> Optional[tuple]:
    """Find session by code and return session details."""
    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.code == session_code).first()
            if session:
                return (session.id, session.user_id, session.assessment_id)
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def _validate_session_ownership(
    session_code: str, session_user_id: int, session_assessment_id: int, internal_user_id: int, assessment_id: int
):
    """Validate that session belongs to the correct user and assessment."""
    if session_user_id != internal_user_id or session_assessment_id != assessment_id:
        error(
            f"Session code {session_code} mismatch: user ({session_user_id} vs {internal_user_id}) "
            f"or assessment ({session_assessment_id} vs {assessment_id})"
        )
        raise HTTPException(
            status_code=403,
            detail="Session code mismatch or unauthorized.",
        )


def _update_session_to_in_progress(session_db_id: int) -> int:
    """Update session status to in_progress and return session ID."""
    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.id == session_db_id).first()
            if session:
                if session.status in ["pending", "in_progress"]:
                    session.status = "in_progress"
                    if session.started_at is None:
                        session.started_at = func.now()
                    db.flush()
                return session.id
            return session_db_id

    except SQLAlchemyError as e:
        log_database_error("update", "sessions", e, session_db_id=session_db_id)
        return session_db_id


def get_or_create_session(user_id_external: str, assessment_id: int, session_code: str):
    """
    Get or create a session for the user and assessment.
    If session exists by code, it updates its status to 'in_progress' if 'pending'.
    """
    try:
        internal_user_id = get_or_create_user(user_id_external)

        # Check if session exists for this code
        session_result = _find_session_by_code(session_code)

        if session_result:
            session_db_id, session_user_id, session_assessment_id = session_result

            # Validate session ownership
            _validate_session_ownership(
                session_code, session_user_id, session_assessment_id, internal_user_id, assessment_id
            )

            # Update session to in_progress
            return _update_session_to_in_progress(session_db_id)

        # Session code does not exist
        error(f"Session code {session_code} not found. Cannot create on-the-fly in get_or_create_session.")
        return error_response(
            message="Invalid session code.",
            code=status.HTTP_404_NOT_FOUND,
            error_type="NotFound",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error in get_or_create_session: {e}", exc_info=True)
        return error_response(
            message="Error processing session.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


def expire_sessions():
    """
    Background task to mark sessions as expired if past completed_at and not completed.
    This function runs periodically to check for expired sessions.
    """
    try:
        debug("Running session expiry check...")

        with get_db_context() as db:
            # Find sessions that are past their completed_at time and still in progress
            expired_sessions = (
                db.query(SessionModel)
                .filter(
                    and_(
                        SessionModel.status == "in_progress",
                        SessionModel.completed_at.isnot(None),
                        SessionModel.completed_at < func.now(),
                    )
                )
                .all()
            )

            if expired_sessions:
                # Update status to expired
                for session in expired_sessions:
                    session.status = "expired"

                expired_codes = [session.code for session in expired_sessions]
                info(f"Expired {len(expired_sessions)} sessions: {expired_codes}")
                db.flush()
            else:
                debug("No sessions to expire")

    except SQLAlchemyError as e:
        log_database_error("update", "sessions", e)
        error(f"Database error during session expiry check: {str(e)}", exc_info=True)
    except Exception as e:
        error(f"Error during session expiry check: {str(e)}", exc_info=True)


async def periodic_session_expiry():
    """
    Periodic task to run session expiry checks every 5 minutes.
    """
    while True:
        try:
            expire_sessions()
            # Wait 5 minutes before next check
            await asyncio.sleep(300)
        except Exception as e:
            error(f"Error in periodic session expiry: {str(e)}")
            # Wait 1 minute before retrying on error
            await asyncio.sleep(60)


def validate_session_for_question(session_code: str) -> tuple[str, dict]:
    """Validate session and return decoded session code and session details."""
    decoded_session_code = validate_session_code_format(session_code)
    session_details = get_session_for_start_or_validation(decoded_session_code)

    if not session_details:
        return None, error_response(
            message="Invalid or expired session code.",
            code=status.HTTP_404_NOT_FOUND,
            error_type="NotFound",
        )

    # Auto-start session if it's still pending
    if session_details.get("session_status") == "pending":
        session_id = session_details.get("id")
        if session_id:
            start_session_in_db(session_id)
            session_details["session_status"] = "in_progress"

    # Transform session details to expected format
    formatted_session_details = {
        "session_id": session_details["id"],
        "assessment_id": session_details["assessment_id"],
        "assessment_name": session_details["assessment_name"],
        "is_final": session_details["is_final"],
        "session_status": session_details["session_status"],
        "question_selection_mode": session_details["question_selection_mode"],
        "user_id": session_details["user_id"],
        "username": session_details["username"],
        "attempted_questions": [],  # Will be populated later if needed
    }

    return decoded_session_code, formatted_session_details


def get_session_response_data(
    session_details: dict,
    is_correct: bool,
    correct_answer_key: str,
    correct_answer_value: str,
    normalized_session_code: str,
) -> dict:
    """Get response data for the session."""
    current_score = get_session_score(normalized_session_code)
    remaining_time_seconds = session_details.get("remaining_time_seconds", 0)

    attempted_questions_count = get_session_attempted_questions_count(normalized_session_code)

    # Get question selection mode from session details
    question_selection_mode = session_details.get("question_selection_mode", "dynamic")

    return {
        "is_correct": is_correct,
        "correct_answer_key": correct_answer_key,
        "correct_answer_value": correct_answer_value,
        "current_score": current_score,
        "remaining_time_seconds": remaining_time_seconds,
        "attempted_questions_count": attempted_questions_count,
        "question_selection_mode": question_selection_mode,
    }


def validate_session_and_user(session_code: str, user_id: str) -> tuple[str, dict, str]:
    """Validate session and user, return normalized session code, session details, and validated user_id."""
    normalized_session_code = validate_session_code_format(session_code)

    session_details = get_session_details(normalized_session_code)
    if not session_details:
        raise_http_exception(status_code=404, detail="Invalid session code")

    session_user_id = get_session_user_id(normalized_session_code)
    if not session_user_id:
        raise_http_exception(status_code=404, detail="User not found for this session")

    if session_user_id != user_id:
        warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")

    return normalized_session_code, session_details, session_user_id


# from utils


def update_session_status(session_code: str, updates: Dict[str, Any]) -> bool:
    """
    Update session status in database.

    Args:
        session_code: Session code to update
        updates: Dictionary of fields to update

    Returns:
        True if update was successful
    """
    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.code == session_code).first()

            if not session:
                return False

            # Apply updates
            for field, value in updates.items():
                if hasattr(session, field):
                    setattr(session, field, value)

            # Update timestamp
            session.updated_at = func.now()
            db.flush()
            return True

    except SQLAlchemyError as e:
        log_database_error("update", "sessions", e, session_code=session_code, updates=updates)
        return False
    except Exception as e:
        error(f"Failed to update session status: {e}")
        return False


def get_session_score(session_code: str) -> float:
    """
    Calculate current score for a session.

    Args:
        session_code: Session code to check

    Returns:
        Current score
    """
    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.code == session_code).first()

            if not session:
                return 0.0

            total_score = db.query(func.sum(UserAnswer.score)).filter(UserAnswer.session_id == session.id).scalar()

            return float(total_score) if total_score else 0.0

    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, session_code=session_code)
        return 0.0


def get_session_attempted_questions_count(session_code: str) -> int:
    """
    Get the number of attempted questions for a session.

    Args:
        session_code: Session code to check

    Returns:
        Number of attempted questions
    """
    try:
        with get_db_context() as db:
            session = db.query(SessionModel).filter(SessionModel.code == session_code).first()

            if not session:
                return 0

            count = db.query(func.count(UserAnswer.id)).filter(UserAnswer.session_id == session.id).scalar()

            return count if count else 0

    except SQLAlchemyError as e:
        log_database_error("select", "user_answers", e, session_code=session_code)
        return 0


def get_session_details(session_code: str) -> Optional[Dict[str, Any]]:
    """
    Get session details from database.

    Args:
        session_code: Session code to lookup

    Returns:
        Dictionary containing session details or None if not found
    """
    try:
        normalized_code = validate_session_code_format(session_code)

        with get_db_context() as db:
            result = (
                db.query(
                    SessionModel.id.label("session_id"),
                    SessionModel.user_id,
                    SessionModel.assessment_id,
                    SessionModel.code.label("session_code"),
                    SessionModel.started_at.label("start_time"),
                    SessionModel.completed_at.label("end_time"),
                    SessionModel.status,
                    SessionModel.created_at,
                    Assessment.name.label("assessment_name"),
                    Assessment.description.label("assessment_description"),
                    Assessment.question_selection_mode,
                    Assessment.duration_minutes.label("assessment_duration"),
                    User.external_id.label("user_external_id"),
                    User.display_name.label("user_display_name"),
                    User.email.label("user_email"),
                )
                .outerjoin(Assessment, SessionModel.assessment_id == Assessment.id)
                .outerjoin(User, SessionModel.user_id == User.id)
                .filter(SessionModel.code == normalized_code)
                .first()
            )

            if result:
                return {
                    "session_id": result.session_id,
                    "user_id": result.user_id,
                    "assessment_id": result.assessment_id,
                    "session_code": result.session_code,
                    "start_time": result.start_time,
                    "end_time": result.end_time,
                    "status": result.status,
                    "created_at": result.created_at,
                    "assessment_name": result.assessment_name,
                    "assessment_description": result.assessment_description,
                    "question_selection_mode": result.question_selection_mode,
                    "assessment_duration": result.assessment_duration,
                    "user_external_id": result.user_external_id,
                    "user_display_name": result.user_display_name,
                    "user_email": result.user_email,
                }
            return None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def get_session_user_id(session_code: str) -> Optional[str]:
    """
    Get the external user ID for a session.

    Args:
        session_code: Session code to lookup

    Returns:
        External user ID or None if not found
    """
    try:
        normalized_code = validate_session_code_format(session_code)

        with get_db_context() as db:
            result = (
                db.query(User.external_id)
                .join(SessionModel, SessionModel.user_id == User.id)
                .filter(SessionModel.code == normalized_code)
                .first()
            )

            return result.external_id if result else None

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def _parse_user_identifier(user_id: str) -> tuple[str, str, str]:
    """Parse user identifier and return email, username, and external_id."""
    if "@" in user_id:
        email = user_id
        username = user_id.split("@")[0]
        external_id = user_id  # Use full email as external_id to avoid conflicts
    else:
        email = None
        username = user_id
        external_id = username

    return email, username, external_id


def _find_user_by_email(email: str) -> Optional[int]:
    """
    Find user by email by calling the data access layer and return the user ID.
    """
    if not email:
        return None
    # Call the data access function to get the user data
    user_data = db_get_user_by_email(email)
    # Return just the ID, as per the original function's contract
    return user_data["id"] if user_data else None


def _find_user_by_external_id(username: str) -> Optional[int]:
    """
    Find user by external_id by calling the data access layer and return the user ID.
    """
    # Call the data access function to get the user data
    user_data = db_get_user_by_external_id(username)
    # Return just the ID, as per the original function's contract
    return user_data["id"] if user_data else None


def _update_user_email_if_needed(user_id: int, email: str):
    """
    Update user email if needed, handling the logic and transaction commit.
    """
    if email:
        # Call the data access function to perform the update
        rows_updated = db_update_user_email(user_id, email)
        return rows_updated > 0  # Return True if email was updated, False otherwise
        # No need to commit here as get_db_context() handles the transaction


def _create_new_user(external_id: str, email: str, username: str) -> int:
    """
    Create a new user by calling the data access layer and committing the transaction.
    """
    # Call the data access function to insert the user and get the new ID
    user_internal_id = db_create_user(external_id, email, username)
    # No need to commit here as get_db_context() handles the transaction
    return user_internal_id


def get_or_create_user(user_id):
    """
    Get or create a user record and return the internal ID.
    If user_id is an email, extract the username part (before @) for external_id and display_name.
    First checks if a user with the same email exists, then checks by external_id.
    """
    try:
        # Parse user identifier
        email, username, external_id = _parse_user_identifier(user_id)

        # First check if a user with this email exists
        user_id_by_email = _find_user_by_email(email)
        if user_id_by_email:
            return user_id_by_email

        # Then check if user exists by external_id
        user_id_by_external = _find_user_by_external_id(username)
        if user_id_by_external:
            # Update email if needed
            _update_user_email_if_needed(user_id_by_external, email)
            return user_id_by_external

        # Create new user
        return _create_new_user(external_id, email, username)

    except Exception as e:
        error(f"Error in get_or_create_user: {e}")
        raise


# Legacy utility functions removed - using SQLAlchemy ORM instead


def validate_session_code_format(session_code_input: str) -> str:
    """
    Validate and normalize session code input.

    Args:
        session_code_input: Either a 6-digit session code or an encoded hash

    Returns:
        6-digit session code string

    Raises:
        HTTPException: If session code is invalid
    """
    debug(
        f"Validating session code: '{session_code_input}' "
        f"(type: {type(session_code_input)}, len: {len(session_code_input) if session_code_input else 'None'})"
    )

    # Handle None or empty string
    if not session_code_input:
        error("Empty session code provided")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session code cannot be empty",
        )

    # Ensure session_code_input is a string
    session_code_input = str(session_code_input)

    # Check if the input looks like a serialized object or contains invalid characters
    invalid_patterns = ["{", 'id":', 'session_code":', "assessment_", "user_id", "created_at", "status"]
    if any(pattern in session_code_input for pattern in invalid_patterns):
        error(f"Received serialized object instead of session code: {session_code_input[:200]}...")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session code format. Expected 6-digit code or hash, got serialized object.",
        )

    # Additional check for length - session codes should be reasonable length
    if len(session_code_input) > 100:  # Hashes are typically much shorter than 100 chars
        error(f"Session code input too long ({len(session_code_input)} chars): {session_code_input[:100]}...")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session code format. Input too long.",
        )

    # Check if it's already a 6-digit code
    if session_code_input.isdigit() and len(session_code_input) == 6:
        debug(f"Session code is already 6-digit: {session_code_input}")
        return session_code_input

    # Try to decode as hash
    decoded_code = decode_session_code(session_code_input)
    if decoded_code:
        debug(f"Decoded hash '{session_code_input}' to: {decoded_code}")
        return decoded_code

    # If neither worked, raise an error
    error(f"Failed to decode session code: '{session_code_input}'")
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Invalid session code format. Must be either a 6-digit code or valid hash.",
    )
