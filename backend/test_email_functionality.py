#!/usr/bin/env python3
"""
Test script for email functionality.
This script tests the email filtering logic and email service functionality.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_email_filtering():
    """Test the email filtering logic."""
    print("Testing email filtering logic...")
    
    # Mock the email service for testing
    class MockEmailService:
        def should_send_email(self, email: str) -> bool:
            """Check if email should be sent based on domain filtering."""
            if not email or "@" not in email:
                return False
            
            domain = email.split("@")[1].lower()
            
            # Skip improwised.com emails
            if domain == "improwised.com":
                print(f"  ✓ Skipping email to improwised.com address: {email}")
                return False
            
            print(f"  ✓ Will send email to: {email}")
            return True
    
    email_service = MockEmailService()
    
    # Test cases
    test_emails = [
        "<EMAIL>",  # Should be filtered out
        "<EMAIL>",  # Should be filtered out
        "<EMAIL>",  # Should be sent
        "<EMAIL>",  # Should be sent
        "<EMAIL>",  # Should be sent
        "invalid-email",  # Should be filtered out (invalid format)
        "",  # Should be filtered out (empty)
    ]
    
    print("\nTesting email addresses:")
    for email in test_emails:
        should_send = email_service.should_send_email(email)
        status = "SEND" if should_send else "SKIP"
        print(f"  {email:<25} -> {status}")
    
    print("\n✅ Email filtering test completed!")

def test_session_data_structure():
    """Test the session data structure for email sending."""
    print("\nTesting session data structure...")
    
    # Mock session data as it would be returned from _process_single_user_session
    mock_sessions = [
        {
            "id": 1,
            "username": "john.doe",
            "email": "<EMAIL>",  # Should be filtered
            "sessionCode": "123456",
            "sessionDbId": 1,
        },
        {
            "id": 2,
            "username": "jane.smith",
            "email": "<EMAIL>",  # Should be sent
            "sessionCode": "789012",
            "sessionDbId": 2,
        },
        {
            "id": 3,
            "username": "bob.wilson",
            "email": "<EMAIL>",  # Should be sent
            "sessionCode": "345678",
            "sessionDbId": 3,
        }
    ]
    
    # Mock email service
    class MockEmailService:
        def should_send_email(self, email: str) -> bool:
            if not email or "@" not in email:
                return False
            domain = email.split("@")[1].lower()
            return domain != "improwised.com"
    
    email_service = MockEmailService()
    
    # Filter sessions that should receive emails
    sessions_to_email = []
    for session in mock_sessions:
        email = session.get("email")
        if email and email_service.should_send_email(email):
            sessions_to_email.append(session)
            print(f"  ✓ Will send email to: {session['username']} ({email})")
        else:
            print(f"  ✗ Skipping email to: {session['username']} ({email})")
    
    print(f"\nTotal sessions created: {len(mock_sessions)}")
    print(f"Emails to be sent: {len(sessions_to_email)}")
    print(f"Emails filtered out: {len(mock_sessions) - len(sessions_to_email)}")
    
    print("\n✅ Session data structure test completed!")

def test_quiz_link_generation():
    """Test quiz link generation logic."""
    print("\nTesting quiz link generation...")
    
    # Mock environment variables
    os.environ["FRONTEND_URL"] = "http://localhost:5173"
    
    # Mock session data
    session_code = "123456"
    frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")
    
    # Mock the encode_session_code function
    def mock_encode_session_code(code):
        return f"encoded_{code}"
    
    hashed_session_code = mock_encode_session_code(session_code)
    quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"
    
    print(f"  Session Code: {session_code}")
    print(f"  Hashed Code: {hashed_session_code}")
    print(f"  Quiz Link: {quiz_link}")
    
    expected_link = "http://localhost:5173/quiz/encoded_123456"
    if quiz_link == expected_link:
        print("  ✅ Quiz link generation is correct!")
    else:
        print(f"  ❌ Quiz link generation failed. Expected: {expected_link}")
    
    print("\n✅ Quiz link generation test completed!")

if __name__ == "__main__":
    print("🧪 Testing HERBIT Email Functionality")
    print("=" * 50)
    
    test_email_filtering()
    test_session_data_structure()
    test_quiz_link_generation()
    
    print("\n🎉 All tests completed!")
    print("\nNext steps:")
    print("1. Install email dependencies: pip install aiosmtplib email-validator jinja2")
    print("2. Configure SMTP settings in your .env file")
    print("3. Test with real email sending in development environment")
