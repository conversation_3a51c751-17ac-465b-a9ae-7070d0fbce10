#!/usr/bin/env python3
"""
Test script to demonstrate the complete email flow.
This simulates the session creation process with email filtering.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def simulate_session_creation():
    """Simulate the complete session creation and email flow."""
    print("🧪 Testing Complete Email Flow")
    print("=" * 50)
    
    # Mock assessment data
    assessment_data = {
        "id": 1,
        "name": "Python Developer Assessment",
        "description": "Comprehensive Python skills evaluation"
    }
    
    # Mock user input (as it would come from the frontend)
    user_input = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    
    print(f"📋 Assessment: {assessment_data['name']}")
    print(f"👥 User Input: {user_input}")
    print()
    
    # Process user identifiers (simulate backend processing)
    user_identifiers = [identifier.strip() for identifier in user_input.split(",")]
    
    print("🔄 Processing User Identifiers:")
    created_sessions = []
    
    for identifier in user_identifiers:
        # Simulate _process_user_identifier logic
        if "@" in identifier:
            email = identifier.lower()
            display_name = email.split("@")[0]
        else:
            display_name = identifier
            email = f"{display_name.lower()}@example.com"
        
        # Simulate session creation
        session_code = f"{len(created_sessions) + 1:06d}"  # Simple session code
        session_data = {
            "id": len(created_sessions) + 1,
            "username": display_name,
            "email": email,
            "sessionCode": session_code,
            "sessionDbId": len(created_sessions) + 1,
        }
        created_sessions.append(session_data)
        print(f"  ✅ Created session for {display_name} ({email}) - Code: {session_code}")
    
    print(f"\n📊 Total Sessions Created: {len(created_sessions)}")
    print()
    
    # Simulate email sending process
    print("📧 Email Sending Process:")
    
    # Mock email service filtering
    def should_send_email(email):
        if not email or "@" not in email:
            return False
        domain = email.split("@")[1].lower()
        return domain != "improwised.com"
    
    emails_sent = 0
    emails_failed = 0
    
    for session in created_sessions:
        email = session["email"]
        username = session["username"]
        session_code = session["sessionCode"]
        
        if should_send_email(email):
            # Simulate successful email sending
            quiz_link = f"http://localhost:5173/quiz/encoded_{session_code}"
            print(f"  ✅ Email sent to {username} ({email})")
            print(f"     Quiz Link: {quiz_link}")
            emails_sent += 1
        else:
            print(f"  ❌ Email filtered out: {username} ({email}) - improwised.com domain")
            emails_failed += 1
    
    print()
    print("📈 Email Summary:")
    print(f"  📧 Emails Sent: {emails_sent}")
    print(f"  🚫 Emails Filtered: {emails_failed}")
    print(f"  📊 Total Sessions: {len(created_sessions)}")
    
    # Simulate API response
    print()
    print("🔄 API Response:")
    response_data = {
        "status_code": 200,
        "message": f"Generated {len(created_sessions)} session codes successfully. Sent {emails_sent} email invitations.",
        "data": {
            "sessions": created_sessions,
            "emails_sent": emails_sent,
            "emails_failed": emails_failed
        }
    }
    
    print(f"  Status: {response_data['status_code']}")
    print(f"  Message: {response_data['message']}")
    print(f"  Sessions: {len(response_data['data']['sessions'])}")
    print(f"  Emails Sent: {response_data['data']['emails_sent']}")
    print(f"  Emails Failed: {response_data['data']['emails_failed']}")
    
    print()
    print("✅ Complete Flow Test Successful!")
    print()
    print("📋 Summary:")
    print("  1. ✅ Sessions created for all users")
    print("  2. ✅ Emails sent only to non-improwised.com addresses")
    print("  3. ✅ improwised.com addresses filtered out")
    print("  4. ✅ API response includes email statistics")
    print("  5. ✅ Frontend will show email preview and results")

def demonstrate_frontend_preview():
    """Demonstrate how the frontend email preview works."""
    print("\n" + "=" * 50)
    print("🖥️  Frontend Email Preview Demo")
    print("=" * 50)
    
    test_inputs = [
        "<EMAIL>,<EMAIL>",
        "user1,<EMAIL>,<EMAIL>",
        "<EMAIL>,<EMAIL>,<EMAIL>"
    ]
    
    def should_send_email(email):
        if not email or "@" not in email:
            return False
        domain = email.split("@")[1].lower()
        return domain != "improwised.com"
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n📝 Test Case {i}: {user_input}")
        
        entries = [entry.strip() for entry in user_input.split(",") if entry.strip()]
        email_entries = [entry for entry in entries if "@" in entry]
        
        email_preview = []
        for email in email_entries:
            email_preview.append({
                "address": email,
                "willReceiveEmail": should_send_email(email)
            })
        
        print("   📧 Email Preview:")
        for email_info in email_preview:
            status = "✅" if email_info["willReceiveEmail"] else "❌"
            note = "(will receive email)" if email_info["willReceiveEmail"] else "(improwised.com - no email)"
            print(f"     {status} {email_info['address']} {note}")
        
        emails_to_send = sum(1 for e in email_preview if e["willReceiveEmail"])
        emails_filtered = len(email_preview) - emails_to_send
        
        print(f"   📊 {emails_to_send} emails will be sent, {emails_filtered} filtered out")

if __name__ == "__main__":
    simulate_session_creation()
    demonstrate_frontend_preview()
    
    print("\n🎉 All demonstrations completed!")
    print("\n📚 Next Steps:")
    print("1. Install email dependencies: pip install aiosmtplib email-validator jinja2")
    print("2. Configure SMTP settings in .env file")
    print("3. Test with real session creation in the application")
    print("4. Verify email preview works in the frontend")
    print("5. Check that emails are sent only to non-improwised.com addresses")
